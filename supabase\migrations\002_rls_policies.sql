-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reimbursements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.currency_rates ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Businesses table policies
CREATE POLICY "Users can view own businesses" ON public.businesses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own businesses" ON public.businesses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own businesses" ON public.businesses
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own businesses" ON public.businesses
  FOR DELETE USING (auth.uid() = user_id);

-- Subscriptions table policies
CREATE POLICY "Users can view subscriptions for their businesses" ON public.subscriptions
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert subscriptions for their businesses" ON public.subscriptions
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update subscriptions for their businesses" ON public.subscriptions
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete subscriptions for their businesses" ON public.subscriptions
  FOR DELETE USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

-- Payments table policies
CREATE POLICY "Users can view payments for their subscriptions" ON public.payments
  FOR SELECT USING (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert payments for their subscriptions" ON public.payments
  FOR INSERT WITH CHECK (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update payments for their subscriptions" ON public.payments
  FOR UPDATE USING (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete payments for their subscriptions" ON public.payments
  FOR DELETE USING (
    subscription_id IN (
      SELECT s.id FROM public.subscriptions s
      JOIN public.businesses b ON s.business_id = b.id
      WHERE b.user_id = auth.uid()
    )
  );

-- Reimbursements table policies
CREATE POLICY "Users can view reimbursements for their businesses" ON public.reimbursements
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert reimbursements for their businesses" ON public.reimbursements
  FOR INSERT WITH CHECK (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update reimbursements for their businesses" ON public.reimbursements
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete reimbursements for their businesses" ON public.reimbursements
  FOR DELETE USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE user_id = auth.uid()
    )
  );

-- Notifications table policies
CREATE POLICY "Users can view own notifications" ON public.notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON public.notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own notifications" ON public.notifications
  FOR DELETE USING (auth.uid() = user_id);

-- Currency rates table policies (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view currency rates" ON public.currency_rates
  FOR SELECT USING (auth.role() = 'authenticated');

-- Service role can insert currency rates
CREATE POLICY "Service role can insert currency rates" ON public.currency_rates
  FOR INSERT WITH CHECK (auth.role() = 'service_role');
