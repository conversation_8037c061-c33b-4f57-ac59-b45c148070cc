export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          preferred_currency: string | null
          timezone: string | null
          email_notifications: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          preferred_currency?: string | null
          timezone?: string | null
          email_notifications?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          preferred_currency?: string | null
          timezone?: string | null
          email_notifications?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      businesses: {
        Row: {
          id: string
          user_id: string
          business_name: string
          business_description: string | null
          business_reference: string | null
          contact_email: string | null
          contact_phone: string | null
          logo_url: string | null
          reimbursement_timeline_days: number | null
          business_category: string | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          business_name: string
          business_description?: string | null
          business_reference?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          logo_url?: string | null
          reimbursement_timeline_days?: number | null
          business_category?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          business_name?: string
          business_description?: string | null
          business_reference?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          logo_url?: string | null
          reimbursement_timeline_days?: number | null
          business_category?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          business_id: string
          service_name: string
          provider: string
          provider_logo_url: string | null
          amount: number
          currency: string
          billing_frequency: string | null
          next_due_date: string
          category: string | null
          subscription_tier: string | null
          contract_start_date: string | null
          contract_end_date: string | null
          auto_renewal: boolean | null
          cancellation_policy: string | null
          status: string | null
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          business_id: string
          service_name: string
          provider: string
          provider_logo_url?: string | null
          amount: number
          currency: string
          billing_frequency?: string | null
          next_due_date: string
          category?: string | null
          subscription_tier?: string | null
          contract_start_date?: string | null
          contract_end_date?: string | null
          auto_renewal?: boolean | null
          cancellation_policy?: string | null
          status?: string | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          business_id?: string
          service_name?: string
          provider?: string
          provider_logo_url?: string | null
          amount?: number
          currency?: string
          billing_frequency?: string | null
          next_due_date?: string
          category?: string | null
          subscription_tier?: string | null
          contract_start_date?: string | null
          contract_end_date?: string | null
          auto_renewal?: boolean | null
          cancellation_policy?: string | null
          status?: string | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          subscription_id: string
          original_amount: number
          original_currency: string
          kwd_amount: number
          exchange_rate: number
          payment_date: string
          payment_method: string | null
          receipt_url: string | null
          transaction_notes: string | null
          payment_status: string | null
          manual_rate_override: boolean | null
          auto_create_reimbursement: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          subscription_id: string
          original_amount: number
          original_currency: string
          kwd_amount: number
          exchange_rate: number
          payment_date: string
          payment_method?: string | null
          receipt_url?: string | null
          transaction_notes?: string | null
          payment_status?: string | null
          manual_rate_override?: boolean | null
          auto_create_reimbursement?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          subscription_id?: string
          original_amount?: number
          original_currency?: string
          kwd_amount?: number
          exchange_rate?: number
          payment_date?: string
          payment_method?: string | null
          receipt_url?: string | null
          transaction_notes?: string | null
          payment_status?: string | null
          manual_rate_override?: boolean | null
          auto_create_reimbursement?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      reimbursements: {
        Row: {
          id: string
          payment_id: string
          business_id: string
          kwd_amount: number
          status: string | null
          request_date: string
          expected_date: string | null
          approval_date: string | null
          payment_date: string | null
          reference_number: string | null
          notes: string | null
          supporting_documents_url: string[] | null
          reimbursement_method: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          payment_id: string
          business_id: string
          kwd_amount: number
          status?: string | null
          request_date: string
          expected_date?: string | null
          approval_date?: string | null
          payment_date?: string | null
          reference_number?: string | null
          notes?: string | null
          supporting_documents_url?: string[] | null
          reimbursement_method?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          payment_id?: string
          business_id?: string
          kwd_amount?: number
          status?: string | null
          request_date?: string
          expected_date?: string | null
          approval_date?: string | null
          payment_date?: string | null
          reference_number?: string | null
          notes?: string | null
          supporting_documents_url?: string[] | null
          reimbursement_method?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          type: string
          title: string
          message: string
          read: boolean | null
          action_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: string
          title: string
          message: string
          read?: boolean | null
          action_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: string
          title?: string
          message?: string
          read?: boolean | null
          action_url?: string | null
          created_at?: string
        }
      }
      currency_rates: {
        Row: {
          id: string
          usd_to_kwd: number
          gbp_to_kwd: number
          recorded_at: string
        }
        Insert: {
          id?: string
          usd_to_kwd: number
          gbp_to_kwd: number
          recorded_at?: string
        }
        Update: {
          id?: string
          usd_to_kwd?: number
          gbp_to_kwd?: number
          recorded_at?: string
        }
      }
    }
  }
}
