-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
  ('business-logos', 'business-logos', true),
  ('provider-logos', 'provider-logos', true),
  ('receipts', 'receipts', false),
  ('reimbursement-docs', 'reimbursement-docs', false),
  ('user-avatars', 'user-avatars', true);

-- Storage policies for business-logos bucket
CREATE POLICY "Users can view business logos" ON storage.objects
  FOR SELECT USING (bucket_id = 'business-logos');

CREATE POLICY "Users can upload business logos for their businesses" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'business-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update business logos for their businesses" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'business-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete business logos for their businesses" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'business-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for provider-logos bucket
CREATE POLICY "Users can view provider logos" ON storage.objects
  FOR SELECT USING (bucket_id = 'provider-logos');

CREATE POLICY "Users can upload provider logos for their businesses" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'provider-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update provider logos for their businesses" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'provider-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete provider logos for their businesses" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'provider-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for receipts bucket
CREATE POLICY "Users can view their receipts" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload their receipts" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their receipts" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their receipts" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for reimbursement-docs bucket
CREATE POLICY "Users can view their reimbursement docs" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'reimbursement-docs' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload their reimbursement docs" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'reimbursement-docs' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their reimbursement docs" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'reimbursement-docs' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their reimbursement docs" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'reimbursement-docs' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for user-avatars bucket
CREATE POLICY "Users can view user avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'user-avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'user-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'user-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own avatar" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'user-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );
