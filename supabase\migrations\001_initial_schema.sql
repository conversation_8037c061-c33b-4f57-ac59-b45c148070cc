-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users profile table (extends auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  preferred_currency TEXT DEFAULT 'KWD',
  timezone TEXT DEFAULT 'Asia/Kuwait',
  email_notifications BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create businesses table
CREATE TABLE public.businesses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  business_name TEXT NOT NULL,
  business_description TEXT,
  business_reference TEXT,
  contact_email TEXT,
  contact_phone TEXT,
  logo_url TEXT,
  reimbursement_timeline_days INTEGER DEFAULT 30,
  business_category TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscriptions table
CREATE TABLE public.subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID REFERENCES public.businesses(id) ON DELETE CASCADE NOT NULL,
  service_name TEXT NOT NULL,
  provider TEXT NOT NULL,
  provider_logo_url TEXT,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL CHECK (currency IN ('USD', 'GBP')),
  billing_frequency TEXT CHECK (billing_frequency IN ('monthly', 'quarterly', 'annual')) DEFAULT 'monthly',
  next_due_date DATE NOT NULL,
  category TEXT,
  subscription_tier TEXT,
  contract_start_date DATE,
  contract_end_date DATE,
  auto_renewal BOOLEAN DEFAULT true,
  cancellation_policy TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'paused')),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payments table
CREATE TABLE public.payments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE NOT NULL,
  original_amount DECIMAL(10,2) NOT NULL,
  original_currency TEXT NOT NULL CHECK (original_currency IN ('USD', 'GBP')),
  kwd_amount DECIMAL(10,2) NOT NULL,
  exchange_rate DECIMAL(10,6) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method TEXT,
  receipt_url TEXT,
  transaction_notes TEXT,
  payment_status TEXT DEFAULT 'completed',
  manual_rate_override BOOLEAN DEFAULT false,
  auto_create_reimbursement BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create reimbursements table
CREATE TABLE public.reimbursements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  payment_id UUID REFERENCES public.payments(id) ON DELETE CASCADE NOT NULL,
  business_id UUID REFERENCES public.businesses(id) ON DELETE CASCADE NOT NULL,
  kwd_amount DECIMAL(10,2) NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'paid')),
  request_date DATE NOT NULL,
  expected_date DATE,
  approval_date DATE,
  payment_date DATE,
  reference_number TEXT UNIQUE,
  notes TEXT,
  supporting_documents_url TEXT[],
  reimbursement_method TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create currency_rates table for historical tracking
CREATE TABLE public.currency_rates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  usd_to_kwd DECIMAL(10,6) NOT NULL,
  gbp_to_kwd DECIMAL(10,6) NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_businesses_user_id ON public.businesses(user_id);
CREATE INDEX idx_businesses_is_active ON public.businesses(is_active);
CREATE INDEX idx_subscriptions_business_id ON public.subscriptions(business_id);
CREATE INDEX idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX idx_subscriptions_next_due_date ON public.subscriptions(next_due_date);
CREATE INDEX idx_payments_subscription_id ON public.payments(subscription_id);
CREATE INDEX idx_payments_payment_date ON public.payments(payment_date);
CREATE INDEX idx_reimbursements_payment_id ON public.reimbursements(payment_id);
CREATE INDEX idx_reimbursements_business_id ON public.reimbursements(business_id);
CREATE INDEX idx_reimbursements_status ON public.reimbursements(status);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_read ON public.notifications(read);
CREATE INDEX idx_currency_rates_recorded_at ON public.currency_rates(recorded_at);
