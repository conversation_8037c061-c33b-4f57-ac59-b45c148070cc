import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  CreditCard, 
  DollarSign, 
  FileText, 
  TrendingUp, 
  Shield, 
  Globe,
  Clock,
  Users
} from 'lucide-react'
import Link from 'next/link'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Welcome - Kuwait Subscription Tracker',
  description: 'Track and manage subscription payments with KWD conversion',
}

const features = [
  {
    icon: CreditCard,
    title: 'Subscription Management',
    description: 'Track all your subscriptions in one place with automatic payment reminders'
  },
  {
    icon: DollarSign,
    title: 'Currency Conversion',
    description: 'Real-time USD/GBP to KWD conversion with historical rate tracking'
  },
  {
    icon: FileText,
    title: 'Expense Reporting',
    description: 'Generate detailed reports for accounting and tax purposes'
  },
  {
    icon: TrendingUp,
    title: 'Analytics Dashboard',
    description: 'Visualize spending trends and optimize your subscription portfolio'
  },
  {
    icon: Shield,
    title: 'Secure & Private',
    description: 'Your financial data is encrypted and protected with enterprise-grade security'
  },
  {
    icon: Globe,
    title: 'Multi-Business Support',
    description: 'Manage subscriptions across multiple businesses from one account'
  }
]

const testimonials = [
  {
    name: 'Ahmed Al-Rashid',
    role: 'Business Owner',
    company: 'Kuwait Tech Solutions',
    content: 'This tool has simplified our subscription management across three businesses. The KWD conversion feature is exactly what we needed.'
  },
  {
    name: 'Fatima Al-Zahra',
    role: 'Financial Manager',
    company: 'Gulf Enterprises',
    content: 'The reporting features have made our monthly financial reviews so much easier. Highly recommended for any Kuwait-based business.'
  },
  {
    name: 'Omar Hassan',
    role: 'Startup Founder',
    company: 'Innovation Hub',
    content: 'Perfect for tracking SaaS subscriptions. The real-time currency conversion saves us hours of manual calculations.'
  }
]

export default function WelcomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <span className="ml-3 text-xl font-bold text-gray-900 dark:text-white">
                Kuwait Subscription Tracker
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/login">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/auth/register">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Badge className="mb-4" variant="secondary">
            <Clock className="w-4 h-4 mr-2" />
            Real-time Currency Conversion
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Manage Subscriptions with
            <span className="text-blue-600 dark:text-blue-400"> KWD Precision</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Track subscription payments across multiple businesses with automatic USD/GBP to KWD conversion, 
            comprehensive reporting, and intelligent reimbursement workflows.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="w-full sm:w-auto">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/auth/login">
              <Button size="lg" variant="outline" className="w-full sm:w-auto">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Everything you need to manage subscriptions
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Built specifically for Kuwait businesses dealing with international subscriptions
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardHeader>
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Trusted by Kuwait businesses
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              See what our customers have to say
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardContent className="pt-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    "{testimonial.content}"
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="ml-3">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {testimonial.name}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {testimonial.role}, {testimonial.company}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 dark:bg-blue-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to streamline your subscription management?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join hundreds of Kuwait businesses already using our platform
          </p>
          <Link href="/auth/register">
            <Button size="lg" variant="secondary">
              Get Started Today
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
            <span className="ml-3 text-xl font-bold">
              Kuwait Subscription Tracker
            </span>
          </div>
          <p className="text-gray-400">
            © 2024 Kuwait Subscription Tracker. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
