"use client"

import { useState, useEffect } from "react"
import { <PERSON>ting<PERSON>, <PERSON>, <PERSON>, Palette, Database, Download, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { SharedLayout } from "@/components/shared-layout"

interface AppSettings {
  notifications: {
    emailNotifications: boolean
    pushNotifications: boolean
    paymentReminders: boolean
    weeklyReports: boolean
    currencyAlerts: boolean
  }
  preferences: {
    defaultCurrency: "USD" | "GBP"
    dateFormat: "MM/DD/YYYY" | "DD/MM/YYYY" | "YYYY-MM-DD"
    timeFormat: "12h" | "24h"
    language: string
    autoRefreshRates: boolean
    compactView: boolean
  }
  privacy: {
    shareUsageData: boolean
    allowAnalytics: boolean
    showOnlineStatus: boolean
  }
  lastUpdated: string
}

const defaultSettings: AppSettings = {
  notifications: {
    emailNotifications: true,
    pushNotifications: true,
    paymentReminders: true,
    weeklyReports: false,
    currencyAlerts: true,
  },
  preferences: {
    defaultCurrency: "USD",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12h",
    language: "en",
    autoRefreshRates: true,
    compactView: false,
  },
  privacy: {
    shareUsageData: false,
    allowAnalytics: true,
    showOnlineStatus: true,
  },
  lastUpdated: new Date().toISOString(),
}

export function SettingsPage() {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    // Load settings from localStorage
    const storedSettings = localStorage.getItem("appSettings")
    if (storedSettings) {
      try {
        const parsedSettings = JSON.parse(storedSettings)
        setSettings({ ...defaultSettings, ...parsedSettings })
      } catch (error) {
        console.error("Failed to parse stored settings:", error)
      }
    }
  }, [])

  const updateSetting = (category: keyof AppSettings, key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [category]: {
        ...(prev[category] as any),
        [key]: value,
      },
      lastUpdated: new Date().toISOString(),
    }))
  }

  const saveSettings = async () => {
    setIsSaving(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Save to localStorage
      localStorage.setItem("appSettings", JSON.stringify(settings))

      alert("Settings saved successfully!")
    } catch (error) {
      alert("Failed to save settings. Please try again.")
    } finally {
      setIsSaving(false)
    }
  }

  const exportData = () => {
    const data = {
      profile: JSON.parse(localStorage.getItem("userProfile") || "{}"),
      subscriptions: JSON.parse(localStorage.getItem("subscriptions") || "[]"),
      payments: JSON.parse(localStorage.getItem("payments") || "[]"),
      reimbursements: JSON.parse(localStorage.getItem("reimbursements") || "[]"),
      settings: settings,
      exportDate: new Date().toISOString(),
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `subscription-data-${new Date().toISOString().split("T")[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const clearAllData = () => {
    if (confirm("Are you sure you want to clear all data? This action cannot be undone.")) {
      localStorage.clear()
      alert("All data has been cleared. Please refresh the page.")
    }
  }

  return (
    <SharedLayout activeUrl="/settings">
      <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">Manage your application preferences and account settings</p>
          </div>

          <Button onClick={saveSettings} disabled={isSaving} className="dark:bg-blue-600 dark:hover:bg-blue-700">
            <Settings className="h-4 w-4 mr-2" />
            {isSaving ? "Saving..." : "Save Settings"}
          </Button>
        </div>

        <div className="grid gap-6">
          {/* Notifications */}
          <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              <CardDescription>Configure how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                </div>
                <Switch
                  id="email-notifications"
                  checked={settings.notifications.emailNotifications}
                  onCheckedChange={(checked) => updateSetting("notifications", "emailNotifications", checked)}
                />
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-notifications">Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive browser push notifications</p>
                </div>
                <Switch
                  id="push-notifications"
                  checked={settings.notifications.pushNotifications}
                  onCheckedChange={(checked) => updateSetting("notifications", "pushNotifications", checked)}
                />
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="payment-reminders">Payment Reminders</Label>
                  <p className="text-sm text-muted-foreground">Get reminded about upcoming payments</p>
                </div>
                <Switch
                  id="payment-reminders"
                  checked={settings.notifications.paymentReminders}
                  onCheckedChange={(checked) => updateSetting("notifications", "paymentReminders", checked)}
                />
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="weekly-reports">Weekly Reports</Label>
                  <p className="text-sm text-muted-foreground">Receive weekly spending summaries</p>
                </div>
                <Switch
                  id="weekly-reports"
                  checked={settings.notifications.weeklyReports}
                  onCheckedChange={(checked) => updateSetting("notifications", "weeklyReports", checked)}
                />
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="currency-alerts">Currency Rate Alerts</Label>
                  <p className="text-sm text-muted-foreground">Get notified of significant rate changes</p>
                </div>
                <Switch
                  id="currency-alerts"
                  checked={settings.notifications.currencyAlerts}
                  onCheckedChange={(checked) => updateSetting("notifications", "currencyAlerts", checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Preferences */}
          <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Preferences
              </CardTitle>
              <CardDescription>Customize your app experience</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="default-currency">Default Currency</Label>
                  <Select
                    value={settings.preferences.defaultCurrency}
                    onValueChange={(value: "USD" | "GBP") => updateSetting("preferences", "defaultCurrency", value)}
                  >
                    <SelectTrigger className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectItem value="USD" className="dark:hover:bg-[#3a3a3d]">
                        USD ($)
                      </SelectItem>
                      <SelectItem value="GBP" className="dark:hover:bg-[#3a3a3d]">
                        GBP (£)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date-format">Date Format</Label>
                  <Select
                    value={settings.preferences.dateFormat}
                    onValueChange={(value) => updateSetting("preferences", "dateFormat", value)}
                  >
                    <SelectTrigger className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectItem value="MM/DD/YYYY" className="dark:hover:bg-[#3a3a3d]">
                        MM/DD/YYYY
                      </SelectItem>
                      <SelectItem value="DD/MM/YYYY" className="dark:hover:bg-[#3a3a3d]">
                        DD/MM/YYYY
                      </SelectItem>
                      <SelectItem value="YYYY-MM-DD" className="dark:hover:bg-[#3a3a3d]">
                        YYYY-MM-DD
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="time-format">Time Format</Label>
                  <Select
                    value={settings.preferences.timeFormat}
                    onValueChange={(value) => updateSetting("preferences", "timeFormat", value)}
                  >
                    <SelectTrigger className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectItem value="12h" className="dark:hover:bg-[#3a3a3d]">
                        12 Hour
                      </SelectItem>
                      <SelectItem value="24h" className="dark:hover:bg-[#3a3a3d]">
                        24 Hour
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select
                    value={settings.preferences.language}
                    onValueChange={(value) => updateSetting("preferences", "language", value)}
                  >
                    <SelectTrigger className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                      <SelectItem value="en" className="dark:hover:bg-[#3a3a3d]">
                        English
                      </SelectItem>
                      <SelectItem value="ar" className="dark:hover:bg-[#3a3a3d]">
                        العربية
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-refresh">Auto-refresh Currency Rates</Label>
                  <p className="text-sm text-muted-foreground">Automatically update rates every 30 minutes</p>
                </div>
                <Switch
                  id="auto-refresh"
                  checked={settings.preferences.autoRefreshRates}
                  onCheckedChange={(checked) => updateSetting("preferences", "autoRefreshRates", checked)}
                />
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="compact-view">Compact View</Label>
                  <p className="text-sm text-muted-foreground">Use a more compact layout for lists</p>
                </div>
                <Switch
                  id="compact-view"
                  checked={settings.preferences.compactView}
                  onCheckedChange={(checked) => updateSetting("preferences", "compactView", checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Privacy & Security */}
          <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Privacy & Security
              </CardTitle>
              <CardDescription>Control your privacy and data sharing preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="usage-data">Share Usage Data</Label>
                  <p className="text-sm text-muted-foreground">Help improve the app by sharing anonymous usage data</p>
                </div>
                <Switch
                  id="usage-data"
                  checked={settings.privacy.shareUsageData}
                  onCheckedChange={(checked) => updateSetting("privacy", "shareUsageData", checked)}
                />
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="analytics">Allow Analytics</Label>
                  <p className="text-sm text-muted-foreground">Enable analytics to help us understand app usage</p>
                </div>
                <Switch
                  id="analytics"
                  checked={settings.privacy.allowAnalytics}
                  onCheckedChange={(checked) => updateSetting("privacy", "allowAnalytics", checked)}
                />
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="online-status">Show Online Status</Label>
                  <p className="text-sm text-muted-foreground">Let others see when you're online</p>
                </div>
                <Switch
                  id="online-status"
                  checked={settings.privacy.showOnlineStatus}
                  onCheckedChange={(checked) => updateSetting("privacy", "showOnlineStatus", checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Data Management */}
          <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data Management
              </CardTitle>
              <CardDescription>Export or clear your data</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Export Data</Label>
                  <p className="text-sm text-muted-foreground">Download all your data as a JSON file</p>
                </div>
                <Button
                  variant="outline"
                  onClick={exportData}
                  className="dark:bg-[#28282B] dark:border-[#3a3a3d] dark:hover:bg-[#3a3a3d] bg-transparent"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>

              <Separator className="dark:bg-[#3a3a3d]" />

              <div className="flex items-center justify-between">
                <div>
                  <Label>Clear All Data</Label>
                  <p className="text-sm text-muted-foreground">Permanently delete all your data</p>
                </div>
                <Button variant="destructive" onClick={clearAllData} className="bg-red-600 hover:bg-red-700">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Data
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* App Information */}
          <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle>App Information</CardTitle>
              <CardDescription>Version and update information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Version:</span>
                  <p className="text-muted-foreground">1.0.0</p>
                </div>
                <div>
                  <span className="font-medium">Last Updated:</span>
                  <p className="text-muted-foreground">
                    {new Date(settings.lastUpdated).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </SharedLayout>
  )
}
