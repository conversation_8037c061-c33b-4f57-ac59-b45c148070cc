# Subscription Tracker Web App - Complete Product Requirements Document (PRD)

## 1. Project Overview

### 1.1 Purpose
A comprehensive web application to track subscription payments and reimbursements for multiple businesses, handling currency conversions between USD/GBP and KWD with full user authentication and onboarding flow.

### 1.2 Core Problem
- Managing monthly subscriptions for multiple businesses (initially 3)
- Tracking payment status and reimbursement status across different currencies
- Handling multi-currency transactions (USD/GBP to KWD)
- Need for secure user authentication and seamless onboarding experience
- Centralized dashboard for financial oversight and reporting

### 1.3 Target Users
- Business owners managing multiple entities
- Financial managers tracking subscription expenses
- Accountants handling reimbursement workflows
- Individual entrepreneurs with multiple business ventures

### 1.4 Current State Analysis
The existing codebase provides:
- Basic subscription management with currency conversion
- Payment tracking with KWD conversion
- Reimbursement request system
- Dashboard with overview cards
- Real-time currency rate fetching
- Local storage for data persistence

**Missing Features to Implement:**
- User authentication and authorization
- Multi-business management
- Welcome/onboarding flow
- Enhanced reporting and analytics
- Notification system
- Database integration (Supabase)
- File upload capabilities
- Advanced filtering and search

## 2. User Journey & Authentication Flow

### 2.1 Welcome Page
**Landing Experience**
- Hero section with value proposition
- Feature highlights with visual icons
- Testimonials or use case examples
- Clear call-to-action buttons (Sign Up, Log In)
- Responsive design for all devices

**Implementation Requirements:**
- Create new `/welcome` route
- Design modern landing page with Tailwind CSS
- Integrate with existing theme system
- Add navigation to auth flows

### 2.2 Authentication Flow
**Registration Process**
- Email/password signup via Supabase Auth
- Email verification requirement
- Profile setup (name, preferences)
- Optional business setup wizard
- Terms of service and privacy policy acceptance

**Login Process**
- Email/password authentication
- "Remember me" functionality
- Password reset via email
- Social login options (Google, Apple) if needed
- Secure session management

**Post-Authentication**
- First-time user onboarding tutorial
- Dashboard redirect for returning users
- Profile completion prompts

**Implementation Requirements:**
- Install and configure Supabase client
- Create auth components (Login, Register, ForgotPassword)
- Implement protected routes
- Add auth context provider
- Create onboarding flow components

## 3. Enhanced Functional Requirements

### 3.1 Business Management
**Add/Edit/Delete Businesses**
- Business name and description
- Business ID/Reference number
- Contact information (email, phone)
- Business logo upload
- Default reimbursement timeline (days)
- Business category/type
- Tax information (if applicable)

**Implementation Requirements:**
- Create business management pages
- Add business selector to existing forms
- Implement file upload for logos
- Update data models to include business_id
- Migrate existing data to support multi-business

### 3.2 Enhanced Subscription Management
**Extended Subscription Fields**
- Service provider with logo
- Associated business selection
- Billing frequency (monthly/quarterly/annual)
- Auto-renewal status toggle
- Category (Software, Services, Marketing, etc.)
- Subscription tier/plan details
- Contract start/end dates
- Cancellation policy notes

**Implementation Requirements:**
- Extend existing subscription form
- Add business association
- Implement provider logo upload
- Add contract management features
- Enhanced categorization system

### 3.3 Enhanced Payment Tracking
**Extended Payment Features**
- Payment method (Credit Card, Bank Transfer, etc.)
- Receipt/invoice file upload
- Transaction notes and comments
- Payment confirmation status
- Manual exchange rate override option

**Implementation Requirements:**
- Extend existing payment form
- Add file upload for receipts
- Implement payment method tracking
- Add manual rate override functionality
- Enhanced payment status management

### 3.4 Enhanced Reimbursement Tracking
**Extended Reimbursement Features**
- Expected reimbursement date (auto-calculated)
- Reference number assignment
- Supporting documents upload
- Approval workflow notifications
- Reimbursement method tracking

**Implementation Requirements:**
- Extend existing reimbursement system
- Add document upload capabilities
- Implement approval workflow
- Add reference number generation
- Enhanced status tracking

### 3.5 Enhanced Dashboard Features
**Overview Cards with Real-time Data**
- Total active subscriptions count ✅ (exists)
- Upcoming payments (next 7, 30, 90 days) ✅ (partial)
- Pending reimbursements with amounts ✅ (exists)
- Total spent this month (by currency) ✅ (exists)
- Outstanding reimbursements total
- Cash flow trends (visual charts)
- Exchange rate alerts
- Quick action buttons ✅ (exists)

**Interactive Charts**
- Monthly spending trends
- Business-wise expense breakdown
- Currency conversion impact
- Reimbursement timeline analysis

**Implementation Requirements:**
- Integrate Chart.js or Recharts for visualizations
- Add business filtering to dashboard
- Implement trend analysis
- Add exchange rate monitoring
- Create interactive chart components

### 3.6 Advanced Reporting
**Monthly Reports**
- Payments by business with filtering
- Currency conversion summary with rates
- Reimbursement status detailed report
- Cash flow analysis with projections
- Tax-ready expense reports
- Subscription usage analytics

**Export Options**
- PDF reports with company branding
- CSV/Excel export with custom fields
- Integration with accounting software
- Scheduled report delivery via email

**Implementation Requirements:**
- Create reporting dashboard
- Implement PDF generation
- Add CSV/Excel export functionality
- Create email scheduling system
- Design report templates

### 3.7 Notification System
**Email Notifications**
- Payment reminders (7, 3, 1 days before due)
- Reimbursement status updates
- Monthly summary reports
- Exchange rate significant changes
- System maintenance alerts

**In-App Notifications**
- Real-time payment confirmations
- Reimbursement approvals
- Subscription renewals
- Dashboard alerts for overdue items

**Implementation Requirements:**
- Integrate email service (Supabase Edge Functions)
- Create notification components
- Implement notification preferences
- Add real-time notification system
- Create notification history

## 4. Technical Implementation

### 4.1 Technology Stack Enhancement
**Current Stack:**
- Next.js 14 ✅
- React 18 ✅
- TypeScript ✅
- Tailwind CSS ✅
- Radix UI components ✅
- Local Storage for data ✅

**Required Additions:**
- Supabase (Database + Auth + Storage)
- React Hook Form with Zod validation ✅ (partially)
- File upload handling
- PDF generation library
- Chart visualization library
- Email service integration

### 4.2 Supabase Configuration
**Database Schema**

```sql
-- Users Table (Extended)
CREATE TABLE users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  preferred_currency TEXT DEFAULT 'KWD',
  timezone TEXT DEFAULT 'Asia/Kuwait',
  email_notifications BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Businesses Table
CREATE TABLE businesses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  business_name TEXT NOT NULL,
  business_description TEXT,
  business_reference TEXT,
  contact_email TEXT,
  contact_phone TEXT,
  logo_url TEXT,
  reimbursement_timeline_days INTEGER DEFAULT 30,
  business_category TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions Table
CREATE TABLE subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  service_name TEXT NOT NULL,
  provider TEXT NOT NULL,
  provider_logo_url TEXT,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL CHECK (currency IN ('USD', 'GBP')),
  billing_frequency TEXT CHECK (billing_frequency IN ('monthly', 'quarterly', 'annual')),
  next_due_date DATE NOT NULL,
  category TEXT,
  subscription_tier TEXT,
  contract_start_date DATE,
  contract_end_date DATE,
  auto_renewal BOOLEAN DEFAULT true,
  cancellation_policy TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'paused')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments Table
CREATE TABLE payments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
  original_amount DECIMAL(10,2) NOT NULL,
  original_currency TEXT NOT NULL CHECK (original_currency IN ('USD', 'GBP')),
  kwd_amount DECIMAL(10,2) NOT NULL,
  exchange_rate DECIMAL(10,6) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method TEXT,
  receipt_url TEXT,
  transaction_notes TEXT,
  payment_status TEXT DEFAULT 'completed',
  manual_rate_override BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reimbursements Table
CREATE TABLE reimbursements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  payment_id UUID REFERENCES payments(id) ON DELETE CASCADE,
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  kwd_amount DECIMAL(10,2) NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'paid')),
  request_date DATE NOT NULL,
  expected_date DATE,
  approval_date DATE,
  payment_date DATE,
  reference_number TEXT UNIQUE,
  notes TEXT,
  supporting_documents_url TEXT[],
  reimbursement_method TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications Table
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Currency Rates Table (for historical tracking)
CREATE TABLE currency_rates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  usd_to_kwd DECIMAL(10,6) NOT NULL,
  gbp_to_kwd DECIMAL(10,6) NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Row Level Security (RLS) Policies**
```sql
-- Enable RLS
ALTER TABLE businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reimbursements ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Policies for businesses
CREATE POLICY "Users can view own businesses" ON businesses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own businesses" ON businesses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own businesses" ON businesses
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own businesses" ON businesses
  FOR DELETE USING (auth.uid() = user_id);

-- Similar policies for other tables...
```

### 4.3 File Storage Configuration
**Supabase Storage Buckets**
- `business-logos`: Business logo uploads
- `provider-logos`: Service provider logos
- `receipts`: Payment receipts and invoices
- `reimbursement-docs`: Supporting documents
- `user-avatars`: User profile pictures

### 4.4 Migration Strategy
**Data Migration Plan**
1. Export existing localStorage data
2. Create migration scripts for each data type
3. Transform data to match new schema
4. Import data with proper user associations
5. Verify data integrity
6. Update application to use Supabase

## 5. Implementation Phases

### Phase 1: Authentication & Core Setup (Week 1-2)
- [ ] Install and configure Supabase
- [ ] Create database schema
- [ ] Implement authentication system
- [ ] Create welcome/landing page
- [ ] Set up protected routes
- [ ] Create user profile management

### Phase 2: Business Management (Week 3)
- [ ] Create business management interface
- [ ] Implement business CRUD operations
- [ ] Add business selection to existing forms
- [ ] Migrate existing data structure
- [ ] Add file upload for business logos

### Phase 3: Enhanced Features (Week 4-5)
- [ ] Extend subscription management
- [ ] Enhance payment tracking
- [ ] Improve reimbursement system
- [ ] Add file upload capabilities
- [ ] Implement advanced filtering

### Phase 4: Reporting & Analytics (Week 6)
- [ ] Create reporting dashboard
- [ ] Implement chart visualizations
- [ ] Add export functionality
- [ ] Create PDF report generation
- [ ] Add trend analysis

### Phase 5: Notifications & Polish (Week 7-8)
- [ ] Implement notification system
- [ ] Add email notifications
- [ ] Create onboarding flow
- [ ] Performance optimization
- [ ] Testing and bug fixes

## 6. Success Metrics

### 6.1 User Engagement
- User registration and retention rates
- Daily/Monthly active users
- Feature adoption rates
- Session duration and frequency

### 6.2 Functional Metrics
- Number of businesses managed per user
- Subscription tracking accuracy
- Payment processing efficiency
- Reimbursement turnaround time

### 6.3 Technical Metrics
- Application performance (load times)
- Data accuracy and consistency
- System uptime and reliability
- Error rates and resolution time

## 7. Risk Assessment

### 7.1 Technical Risks
- **Data Migration Complexity**: Migrating from localStorage to Supabase
  - *Mitigation*: Thorough testing and backup strategies
- **Currency API Reliability**: Dependency on external currency services
  - *Mitigation*: Implement fallback rates and caching
- **File Upload Security**: Handling user-uploaded files
  - *Mitigation*: Implement proper validation and scanning

### 7.2 User Experience Risks
- **Learning Curve**: New authentication and business management features
  - *Mitigation*: Comprehensive onboarding and documentation
- **Data Loss Concerns**: Moving from local to cloud storage
  - *Mitigation*: Clear communication and migration assistance

### 7.3 Business Risks
- **Scope Creep**: Feature requests beyond initial requirements
  - *Mitigation*: Clear phase definitions and change management
- **Performance Impact**: Additional features affecting speed
  - *Mitigation*: Performance monitoring and optimization

## 8. Future Enhancements

### 8.1 Advanced Features
- Mobile application (React Native)
- API integrations with popular services
- Advanced analytics and forecasting
- Multi-language support
- Accounting software integrations

### 8.2 Scalability Considerations
- Multi-tenant architecture
- Advanced user roles and permissions
- Enterprise features (SSO, audit logs)
- White-label solutions
- Advanced reporting and dashboards

## 9. Conclusion

This PRD outlines a comprehensive enhancement plan for the existing Kuwait Subscription Tracker, transforming it from a simple local storage application into a full-featured, multi-business subscription management platform. The phased approach ensures manageable development cycles while delivering value incrementally.

The implementation will leverage the existing solid foundation while adding enterprise-grade features like authentication, multi-business support, advanced reporting, and comprehensive notification systems. The result will be a robust, scalable solution that meets the needs of business owners and financial managers tracking subscription expenses across multiple entities.