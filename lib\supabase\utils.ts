import { createClient } from './client'
import { Database } from './types'

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// User operations
export async function createUserProfile(userData: Inserts<'users'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('users')
    .insert(userData)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function getUserProfile(userId: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) throw error
  return data
}

export async function updateUserProfile(userId: string, updates: Updates<'users'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('users')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', userId)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Business operations
export async function createBusiness(businessData: Inserts<'businesses'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('businesses')
    .insert(businessData)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function getUserBusinesses(userId: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('businesses')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export async function updateBusiness(businessId: string, updates: Updates<'businesses'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('businesses')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', businessId)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function deleteBusiness(businessId: string) {
  const supabase = createClient()
  const { error } = await supabase
    .from('businesses')
    .update({ is_active: false, updated_at: new Date().toISOString() })
    .eq('id', businessId)
  
  if (error) throw error
}

// Subscription operations
export async function createSubscription(subscriptionData: Inserts<'subscriptions'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('subscriptions')
    .insert(subscriptionData)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function getBusinessSubscriptions(businessId: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*')
    .eq('business_id', businessId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export async function updateSubscription(subscriptionId: string, updates: Updates<'subscriptions'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('subscriptions')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', subscriptionId)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function deleteSubscription(subscriptionId: string) {
  const supabase = createClient()
  const { error } = await supabase
    .from('subscriptions')
    .delete()
    .eq('id', subscriptionId)
  
  if (error) throw error
}

// Payment operations
export async function createPayment(paymentData: Inserts<'payments'>) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('payments')
    .insert(paymentData)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function getSubscriptionPayments(subscriptionId: string) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('payments')
    .select('*')
    .eq('subscription_id', subscriptionId)
    .order('payment_date', { ascending: false })
  
  if (error) throw error
  return data
}

// Currency rates operations
export async function saveCurrencyRates(usdToKwd: number, gbpToKwd: number) {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('currency_rates')
    .insert({
      usd_to_kwd: usdToKwd,
      gbp_to_kwd: gbpToKwd,
    })
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function getLatestCurrencyRates() {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('currency_rates')
    .select('*')
    .order('recorded_at', { ascending: false })
    .limit(1)
    .single()
  
  if (error && error.code !== 'PGRST116') throw error
  return data
}

// File upload utilities
export async function uploadFile(bucket: string, path: string, file: File) {
  const supabase = createClient()
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false
    })
  
  if (error) throw error
  return data
}

export async function getFileUrl(bucket: string, path: string) {
  const supabase = createClient()
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path)
  
  return data.publicUrl
}

export async function deleteFile(bucket: string, path: string) {
  const supabase = createClient()
  const { error } = await supabase.storage
    .from(bucket)
    .remove([path])
  
  if (error) throw error
}
