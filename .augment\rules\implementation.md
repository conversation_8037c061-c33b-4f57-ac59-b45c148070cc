---
type: "always_apply"
description: "to remember always"
---
ChatGPT said:

Thought for a couple of seconds
Task:

Load the tasks.json file from your project root.

Iterate over every task defined in tasks.json.

For each task:

Implement the feature or change described, using real, production-ready code—no mocks or simulations allowed.

Verify end-to-end functionality with actual data and integrations.

Run an error check across the codebase:

Compile the TypeScript project and ensure there are no type errors.

Run your linter (e.g. ESLint) and fix any reported issues.

Execute your full suite of unit and integration tests, confirming zero failures.

Mark its "status" (or equivalent flag) as "done" in tasks.json only after all checks pass.

Commit your changes, including the updated source code and the modified tasks.json.

Repeat steps 3–4 until all tasks in tasks.json are fully implemented, tested, and marked complete.

Save and close any open editors or task trackers once finished.