"use client"

import { useState, useEffect, useCallback } from "react"

interface CurrencyRates {
  usdToKwd: number
  gbpToKwd: number
  lastUpdated: string
}

interface CurrencyApiResponse {
  kwd: number
}

const CACHE_KEY = "currency_rates_cache"
const CACHE_DURATION = 30 * 60 * 1000 // 30 minutes in milliseconds
const POLL_INTERVAL = 30 * 60 * 1000 // 30 minutes

export function useCurrencyRates() {
  const [rates, setRates] = useState<CurrencyRates>({
    usdToKwd: 0.307, // fallback rates
    gbpToKwd: 0.385,
    lastUpdated: new Date().toISOString(),
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isOnline, setIsOnline] = useState(true)

  // Load cached rates on mount
  useEffect(() => {
    const cached = localStorage.getItem(CACHE_KEY)
    if (cached) {
      try {
        const cachedData = JSON.parse(cached)
        const cacheAge = Date.now() - new Date(cachedData.timestamp).getTime()

        if (cacheAge < CACHE_DURATION) {
          setRates(cachedData.rates)
          return
        }
      } catch (error) {
        console.warn("Failed to parse cached currency rates:", error)
      }
    }

    // If no valid cache, fetch immediately
    fetchRates()
  }, [])

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  const fetchRates = useCallback(async () => {
    if (!isOnline) {
      setError("Offline - using cached rates")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const [usdResponse, gbpResponse] = await Promise.all([
        fetch("https://cdn.jsdelivr.net/gh/fawazahmed0/currency-api@latest/v1/currencies/usd/kwd.json"),
        fetch("https://cdn.jsdelivr.net/gh/fawazahmed0/currency-api@latest/v1/currencies/gbp/kwd.json"),
      ])

      if (!usdResponse.ok || !gbpResponse.ok) {
        throw new Error("Failed to fetch currency rates")
      }

      const usdData: CurrencyApiResponse = await usdResponse.json()
      const gbpData: CurrencyApiResponse = await gbpResponse.json()

      const newRates: CurrencyRates = {
        usdToKwd: usdData?.kwd || rates.usdToKwd,
        gbpToKwd: gbpData?.kwd || rates.gbpToKwd,
        lastUpdated: new Date().toISOString(),
      }

      setRates(newRates)

      // Cache the new rates
      localStorage.setItem(
        CACHE_KEY,
        JSON.stringify({
          rates: newRates,
          timestamp: new Date().toISOString(),
        }),
      )
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch rates"
      setError(errorMessage)
      console.error("Currency rate fetch error:", err)
    } finally {
      setIsLoading(false)
    }
  }, [isOnline, rates.usdToKwd, rates.gbpToKwd])

  // Set up polling
  useEffect(() => {
    const interval = setInterval(fetchRates, POLL_INTERVAL)
    return () => clearInterval(interval)
  }, [fetchRates])

  const refreshRates = useCallback(() => {
    fetchRates()
  }, [fetchRates])

  return {
    rates,
    isLoading,
    error,
    isOnline,
    refreshRates,
  }
}
