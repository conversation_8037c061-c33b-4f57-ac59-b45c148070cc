"use client"
import { <PERSON>, CreditCard, FileText, TrendingUp, Wallet, Receipt } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useCurrencyRates } from "@/hooks/useCurrencyRates"
import { SharedLayout } from "@/components/shared-layout"

// Mock data for the dashboard
const dashboardData = {
  activeSubscriptions: { count: 24, status: "active" },
  upcomingPayments: {
    usd: 450.0,
    gbp: 320.0,
    kwd: 195.5,
    status: "warning",
  },
  pendingReimbursements: { amount: 85.75, status: "success" },
  totalSpentThisMonth: { amount: 1250.3, status: "neutral" },
}

function getStatusBorderColor(status: string) {
  switch (status) {
    case "success":
      return "border-l-green-500 dark:border-l-green-400"
    case "warning":
      return "border-l-yellow-500 dark:border-l-yellow-400"
    case "error":
      return "border-l-red-500 dark:border-l-red-400"
    case "active":
      return "border-l-blue-500 dark:border-l-blue-400"
    default:
      return "border-l-gray-300 dark:border-l-gray-600"
  }
}

function DashboardContent() {
  const { rates } = useCurrencyRates()

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 dark:bg-[#28282B]">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's your subscription overview.</p>
        </div>
        <Button className="dark:bg-blue-600 dark:hover:bg-blue-700">
          <Bell className="mr-2 h-4 w-4" />
          Notifications
        </Button>
      </div>

      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.activeSubscriptions.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold">{dashboardData.activeSubscriptions.count}</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>

        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.upcomingPayments.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Payments</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="space-y-2">
              {/* Primary KWD Display */}
              <div className="text-2xl font-bold text-blue-700 dark:text-blue-400">
                {(
                  dashboardData.upcomingPayments.usd * rates.usdToKwd +
                  dashboardData.upcomingPayments.gbp * rates.gbpToKwd
                ).toFixed(2)}{" "}
                KWD
              </div>

              {/* Foreign Currency Breakdown */}
              <div className="text-xs text-muted-foreground space-y-1 border-t pt-2 dark:border-t-[#3a3a3d]">
                <div className="flex justify-between">
                  <span className="font-semibold">
                    {(dashboardData.upcomingPayments.usd * rates.usdToKwd).toFixed(2)} KWD
                  </span>
                  <span className="text-gray-500 dark:text-gray-400">
                    (${dashboardData.upcomingPayments.usd.toFixed(2)} USD)
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-semibold">
                    {(dashboardData.upcomingPayments.gbp * rates.gbpToKwd).toFixed(2)} KWD
                  </span>
                  <span className="text-gray-500 dark:text-gray-400">
                    (£{dashboardData.upcomingPayments.gbp.toFixed(2)} GBP)
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.pendingReimbursements.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Reimbursements</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold text-green-700 dark:text-green-400">
              {dashboardData.pendingReimbursements.amount.toFixed(2)} KWD
            </div>
            <p className="text-xs text-muted-foreground">3 pending requests (KWD only)</p>
          </CardContent>
        </Card>

        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.totalSpentThisMonth.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-400">
              {dashboardData.totalSpentThisMonth.amount.toFixed(2)} KWD
            </div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest subscription activities and payments with currency details.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between border-b pb-2 dark:border-b-[#3a3a3d]">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full dark:bg-green-400"></div>
                  <div>
                    <span className="text-sm font-medium">Netflix subscription renewed</span>
                    <div className="text-xs text-muted-foreground">
                      <span className="font-semibold">4.61 KWD</span> (from $15.00 USD)
                    </div>
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">2 hours ago</span>
              </div>
              <div className="flex items-center justify-between border-b pb-2 dark:border-b-[#3a3a3d]">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full dark:bg-blue-400"></div>
                  <div>
                    <span className="text-sm font-medium">Spotify payment processed</span>
                    <div className="text-xs text-muted-foreground">
                      <span className="font-semibold">3.85 KWD</span> (from £10.00 GBP)
                    </div>
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">1 day ago</span>
              </div>
              <div className="flex items-center justify-between border-b pb-2 dark:border-b-[#3a3a3d]">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full dark:bg-orange-400"></div>
                  <div>
                    <span className="text-sm font-medium">Adobe Creative Suite due soon</span>
                    <div className="text-xs text-muted-foreground">
                      <span className="font-semibold">15.35 KWD</span> (from $50.00 USD)
                    </div>
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">3 days ago</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3 dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Manage your subscriptions efficiently.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <CreditCard className="mr-2 h-4 w-4" />
              Add New Subscription
            </Button>
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <Receipt className="mr-2 h-4 w-4" />
              Submit Reimbursement
            </Button>
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <FileText className="mr-2 h-4 w-4" />
              Generate Report
            </Button>
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <TrendingUp className="mr-2 h-4 w-4" />
              View Currency Trends
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export function DashboardLayout({ activeUrl = "/" }: { activeUrl?: string }) {
  return (
    <SharedLayout activeUrl={activeUrl}>
      <DashboardContent />
    </SharedLayout>
  )
}
