"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { User, Mail, Phone, MapPin, Calendar, Save, Upload, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SharedLayout } from "@/components/shared-layout"

interface UserProfile {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  city: string
  country: string
  bio: string
  avatar: string | null
  dateOfBirth: string
  jobTitle: string
  company: string
  createdAt: string
  lastUpdated: string
}

const defaultProfile: UserProfile = {
  firstName: "<PERSON>",
  lastName: "Kuwait",
  email: "<EMAIL>",
  phone: "+965 1234 5678",
  address: "123 Kuwait City Street",
  city: "Kuwait City",
  country: "Kuwait",
  bio: "Subscription management enthusiast",
  avatar: null,
  dateOfBirth: "1990-01-01",
  jobTitle: "Financial Analyst",
  company: "Kuwait Finance House",
  createdAt: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
}

export function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile>(defaultProfile)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [avatarFile, setAvatarFile] = useState<File | null>(null)

  useEffect(() => {
    // Load profile from localStorage
    const storedProfile = localStorage.getItem("userProfile")
    if (storedProfile) {
      try {
        const parsedProfile = JSON.parse(storedProfile)
        setProfile({ ...defaultProfile, ...parsedProfile })
      } catch (error) {
        console.error("Failed to parse stored profile:", error)
      }
    }
  }, [])

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setProfile((prev) => ({ ...prev, [field]: value }))
  }

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setAvatarFile(file)
      // Create a preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        setProfile((prev) => ({ ...prev, avatar: e.target?.result as string }))
      }
      reader.readAsDataURL(file)
    }
  }

  const removeAvatar = () => {
    setProfile((prev) => ({ ...prev, avatar: null }))
    setAvatarFile(null)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const updatedProfile = {
        ...profile,
        lastUpdated: new Date().toISOString(),
      }

      // Save to localStorage
      localStorage.setItem("userProfile", JSON.stringify(updatedProfile))
      setProfile(updatedProfile)
      setIsEditing(false)

      alert("Profile updated successfully!")
    } catch (error) {
      alert("Failed to update profile. Please try again.")
    } finally {
      setIsSaving(false)
    }
  }

  const getInitials = () => {
    return `${profile.firstName.charAt(0)}${profile.lastName.charAt(0)}`.toUpperCase()
  }

  return (
    <SharedLayout activeUrl="/profile">
      <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
            <p className="text-muted-foreground">Manage your personal information and preferences</p>
          </div>

          <div className="flex gap-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  disabled={isSaving}
                  className="dark:bg-[#28282B] dark:border-[#3a3a3d] dark:hover:bg-[#3a3a3d]"
                >
                  Cancel
                </Button>
                <Button onClick={handleSave} disabled={isSaving} className="dark:bg-blue-600 dark:hover:bg-blue-700">
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? "Saving..." : "Save Changes"}
                </Button>
              </>
            ) : (
              <Button onClick={() => setIsEditing(true)} className="dark:bg-blue-600 dark:hover:bg-blue-700">
                <User className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          {/* Avatar Section */}
          <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle>Profile Picture</CardTitle>
              <CardDescription>Update your profile picture</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col items-center space-y-4">
                <Avatar className="h-32 w-32">
                  <AvatarImage src={profile.avatar || undefined} alt="Profile" />
                  <AvatarFallback className="text-2xl bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
                    {getInitials()}
                  </AvatarFallback>
                </Avatar>

                {isEditing && (
                  <div className="flex flex-col gap-2 w-full">
                    {!profile.avatar ? (
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center dark:border-[#3a3a3d]">
                        <Upload className="mx-auto h-8 w-8 text-gray-400" />
                        <Label htmlFor="avatar-upload" className="cursor-pointer">
                          <span className="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
                            Upload Photo
                          </span>
                          <Input
                            id="avatar-upload"
                            type="file"
                            accept="image/*"
                            onChange={handleAvatarUpload}
                            className="hidden"
                          />
                        </Label>
                        <p className="text-xs text-gray-500 mt-1">JPG, PNG up to 5MB</p>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={removeAvatar}
                        className="w-full dark:bg-[#28282B] dark:border-[#3a3a3d] dark:hover:bg-[#3a3a3d] bg-transparent"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Remove Photo
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card className="md:col-span-2 dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>Your basic personal details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={profile.firstName}
                    onChange={(e) => handleInputChange("firstName", e.target.value)}
                    disabled={!isEditing}
                    className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={profile.lastName}
                    onChange={(e) => handleInputChange("lastName", e.target.value)}
                    disabled={!isEditing}
                    className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      disabled={!isEditing}
                      className="pl-10 dark:bg-[#28282B] dark:border-[#3a3a3d]"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="phone"
                      value={profile.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                      disabled={!isEditing}
                      className="pl-10 dark:bg-[#28282B] dark:border-[#3a3a3d]"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">Date of Birth</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={profile.dateOfBirth}
                    onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                    disabled={!isEditing}
                    className="pl-10 dark:bg-[#28282B] dark:border-[#3a3a3d]"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={profile.bio}
                  onChange={(e) => handleInputChange("bio", e.target.value)}
                  disabled={!isEditing}
                  rows={3}
                  className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
                  placeholder="Tell us about yourself..."
                />
              </div>
            </CardContent>
          </Card>

          {/* Professional Information */}
          <Card className="md:col-span-2 dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle>Professional Information</CardTitle>
              <CardDescription>Your work and career details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input
                    id="jobTitle"
                    value={profile.jobTitle}
                    onChange={(e) => handleInputChange("jobTitle", e.target.value)}
                    disabled={!isEditing}
                    className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={profile.company}
                    onChange={(e) => handleInputChange("company", e.target.value)}
                    disabled={!isEditing}
                    className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
            <CardHeader>
              <CardTitle>Address</CardTitle>
              <CardDescription>Your location details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="address">Street Address</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="address"
                    value={profile.address}
                    onChange={(e) => handleInputChange("address", e.target.value)}
                    disabled={!isEditing}
                    className="pl-10 dark:bg-[#28282B] dark:border-[#3a3a3d]"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={profile.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  disabled={!isEditing}
                  className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Select
                  value={profile.country}
                  onValueChange={(value) => handleInputChange("country", value)}
                  disabled={!isEditing}
                >
                  <SelectTrigger className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                    <SelectItem value="Kuwait" className="dark:hover:bg-[#3a3a3d]">
                      Kuwait
                    </SelectItem>
                    <SelectItem value="Saudi Arabia" className="dark:hover:bg-[#3a3a3d]">
                      Saudi Arabia
                    </SelectItem>
                    <SelectItem value="UAE" className="dark:hover:bg-[#3a3a3d]">
                      UAE
                    </SelectItem>
                    <SelectItem value="Qatar" className="dark:hover:bg-[#3a3a3d]">
                      Qatar
                    </SelectItem>
                    <SelectItem value="Bahrain" className="dark:hover:bg-[#3a3a3d]">
                      Bahrain
                    </SelectItem>
                    <SelectItem value="Oman" className="dark:hover:bg-[#3a3a3d]">
                      Oman
                    </SelectItem>
                    <SelectItem value="Other" className="dark:hover:bg-[#3a3a3d]">
                      Other
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Account Information */}
        <Card className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>Account creation and activity details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Account Created:</span>
                <p className="text-muted-foreground">
                  {new Date(profile.createdAt).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
              <div>
                <span className="font-medium">Last Updated:</span>
                <p className="text-muted-foreground">
                  {new Date(profile.lastUpdated).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </SharedLayout>
  )
}
